'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { searchChampions } from '@/data/champions';
import { Champion } from '@/types/champion';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Champion[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const searchResults = searchChampions(query);
    setResults(searchResults);
  }, [query]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleClose = () => {
    setQuery('');
    onClose();
  };

  const handleResultClick = () => {
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm">
      <div className="flex items-start justify-center min-h-screen pt-20 px-4">
        <div className="bg-gray-900 rounded-lg w-full max-w-2xl border border-gray-700">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Search Champions</h2>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="relative mb-6">
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Enter a name or stage name"
                className="w-full bg-gray-800 text-white text-lg font-bold border-0 border-b-2 border-gray-600 focus:border-yellow-400 outline-none py-3 px-0 placeholder-gray-500"
              />
            </div>

            <div className="text-sm text-gray-400 mb-4">
              {results.length} champion{results.length !== 1 ? 's' : ''} found
            </div>

            <div className="max-h-96 overflow-y-auto">
              {results.length > 0 ? (
                <div className="space-y-3">
                  {results.map((champion) => (
                    <Link
                      key={champion.id}
                      href={`/champion/${champion.slug}`}
                      onClick={handleResultClick}
                      className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    >
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-700">
                        <Image
                          src={champion.image}
                          alt={champion.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-white font-semibold group-hover:text-yellow-400 transition-colors">
                          {champion.name}
                        </h3>
                        <p className="text-gray-400 text-sm">{champion.stageName}</p>
                        <p className="text-gray-500 text-xs">{champion.sport}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : query.trim() ? (
                <div className="text-center py-8 text-gray-400">
                  No champions found for "{query}"
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
