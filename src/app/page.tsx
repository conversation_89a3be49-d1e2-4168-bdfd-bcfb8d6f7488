"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { champions } from "@/data/champions";

export default function Home() {
  const [shuffledChampions, setShuffledChampions] = useState(champions);

  const shuffleChampions = () => {
    const shuffled = [...champions].sort(() => Math.random() - 0.5);
    setShuffledChampions(shuffled);
  };

  const sortChampions = () => {
    const sorted = [...champions].sort((a, b) => a.name.localeCompare(b.name));
    setShuffledChampions(sorted);
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Hero Section */}
      <section className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            Ukrainian Champions
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Celebrating the extraordinary achievements of Ukrainian sports
            champions who have brought glory to their nation on the world stage.
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className="text-gray-400">
            {shuffledChampions.length} Champions
          </span>
          <button
            onClick={sortChampions}
            className="px-4 py-2 text-white hover:text-yellow-400 transition-colors border border-gray-600 rounded hover:border-yellow-400"
          >
            Sort
          </button>
          <button
            onClick={shuffleChampions}
            className="px-4 py-2 text-white hover:text-yellow-400 transition-colors border border-gray-600 rounded hover:border-yellow-400"
          >
            Shuffle
          </button>
        </div>

        {/* Champions Grid */}
        <section className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-1 mb-16">
          {shuffledChampions.map((champion) => (
            <Link
              key={champion.id}
              href={`/champion/${champion.slug}`}
              className="group relative aspect-[0.8] overflow-hidden bg-gray-900 hover:scale-105 transition-transform duration-300"
              data-name={champion.name}
              data-stagename={champion.stageName}
            >
              <Image
                src={champion.image}
                alt={champion.name}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
                sizes="(max-width: 768px) 50vw, (max-width: 1024px) 25vw, 14vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <h3 className="font-bold text-sm">{champion.name}</h3>
                <p className="text-xs text-gray-300">{champion.stageName}</p>
              </div>
            </Link>
          ))}
        </section>
      </section>
    </div>
  );
}
